
project_id = "oijoqbyfwbyochcxdutg"

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public"]
max_rows = 1000

[db]
port = 54322

[studio]
enabled = true
port = 54323

[ingest]
enabled = true
port = 54324

[storage]
enabled = true
port = 54325
image_transformation_enabled = true

[auth]
enabled = true
port = 54326
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://localhost:3000"]
jwt_expiry = 3600
refresh_token_rotation_enabled = true
security_update_password_require_reauthentication = true

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false

[edge_functions]
enabled = true
port = 54327

[functions.create-payment]
verify_jwt = true

[functions.verify-payment]
verify_jwt = true
