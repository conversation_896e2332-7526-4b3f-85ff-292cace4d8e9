import { useState } from 'react'
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { Layout } from '@/components/Layout'
import Login from '@/pages/auth/Login'
import Register from '@/pages/auth/Register'
import ForgotPassword from '@/pages/auth/ForgotPassword'
import ResetPassword from '@/pages/auth/ResetPassword'
import Homepage from '@/pages/Homepage'
import Dashboard from '@/pages/Dashboard'
import Inventory from '@/pages/Inventory'
import Services from '@/pages/Services'
import Sales from '@/pages/Sales'
import Expenses from '@/pages/Expenses'
import Quotes from '@/pages/Quotes'
import Contacts from '@/pages/Contacts'
import Transactions from '@/pages/Transactions'
import Settings from '@/pages/Settings'
import Profile from '@/pages/Profile'
import Terms from '@/pages/Terms'
import Privacy from '@/pages/Privacy'
import Receipts from '@/pages/Receipts'
import FinancialRunway from '@/pages/FinancialRunway'
import DemoLogin from '@/pages/auth/DemoLogin'
import ContactUs from '@/pages/ContactUs'
import POS from '@/pages/POS';
// CRM Pages
import CrmContacts from '@/pages/crm/Contacts'
import CrmCompanies from '@/pages/crm/Companies'
import CrmDeals from '@/pages/crm/Deals'
import CrmTasks from '@/pages/crm/Tasks'
import { CurrencyProvider } from "@/contexts/CurrencyContext";
import { SubscriptionProvider } from "@/components/SubscriptionProvider";
import { DataRefreshProvider } from "@/contexts/DataRefreshContext";
import { OnboardingProvider } from '@/components/onboarding/OnboardingProvider';
import { OnboardingTooltip } from '@/components/onboarding/OnboardingTooltip';
import { WelcomeModal } from '@/components/onboarding/WelcomeModal';

function App() {
  const { user, loading } = useAuth()
  const [isOnline, setIsOnline] = useState(navigator.onLine)

  window.addEventListener('online', () => {
    setIsOnline(true)
  })

  window.addEventListener('offline', () => {
    setIsOnline(false)
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <SubscriptionProvider user={user}>
      <CurrencyProvider>
        <DataRefreshProvider>
          <OnboardingProvider>
          <BrowserRouter>
          <Routes>
            <Route path="/login" element={user ? <Navigate to="/dashboard" /> : <Login />} />
            <Route path="/register" element={user ? <Navigate to="/dashboard" /> : <Register />} />
            <Route path="/forgot-password" element={user ? <Navigate to="/dashboard" /> : <ForgotPassword />} />
            <Route path="/reset-password" element={user ? <Navigate to="/dashboard" /> : <ResetPassword />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/demo-login" element={<DemoLogin />} />
            <Route path="/contact" element={<ContactUs />} />

            <Route path="/" element={<Homepage />} />

            <Route
              path="/dashboard"
              element={
                user ? (
                  <Layout user={user}>
                    <Dashboard />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/inventory"
              element={
                user ? (
                  <Layout user={user}>
                    <Inventory />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/services"
              element={
                user ? (
                  <Layout user={user}>
                    <Services />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/sales"
              element={
                user ? (
                  <Layout user={user}>
                    <Sales />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/expenses"
              element={
                user ? (
                  <Layout user={user}>
                    <Expenses />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/quotes"
              element={
                user ? (
                  <Layout user={user}>
                    <Quotes />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/contacts"
              element={
                user ? (
                  <Layout user={user}>
                    <Contacts />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/receipts"
              element={
                user ? (
                  <Layout user={user}>
                    <Receipts />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/financial-runway"
              element={
                user ? (
                  <Layout user={user}>
                    <FinancialRunway />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/pos"
              element={
                user ? (
                  <Layout user={user}>
                    <POS />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            {/* CRM Routes */}
            <Route
              path="/crm/contacts"
              element={
                user ? (
                  <Layout user={user}>
                    <CrmContacts />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/crm/companies"
              element={
                user ? (
                  <Layout user={user}>
                    <CrmCompanies />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/crm/deals"
              element={
                user ? (
                  <Layout user={user}>
                    <CrmDeals />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/crm/tasks"
              element={
                user ? (
                  <Layout user={user}>
                    <CrmTasks />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/transactions"
              element={
                user ? (
                  <Layout user={user}>
                    <Transactions />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/settings"
              element={
                user ? (
                  <Layout user={user}>
                    <Settings />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/profile"
              element={
                user ? (
                  <Layout user={user}>
                    <Profile />
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />

          </Routes>
          </BrowserRouter>

          {/* Onboarding Components */}
          <OnboardingTooltip />
          <WelcomeModal />
          </OnboardingProvider>
        </DataRefreshProvider>
      </CurrencyProvider>
    </SubscriptionProvider>
  );
}

export default App;
