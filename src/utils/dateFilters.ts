import { DateRange } from "react-day-picker";
import { isWithinInterval, parseISO, startOfDay, endOfDay } from "date-fns";

/**
 * Check if a date string is within the specified date range
 */
export const isDateInRange = (dateString: string, dateRange: DateRange | undefined): boolean => {
  if (!dateRange?.from) return true;
  
  try {
    const date = parseISO(dateString);
    const from = startOfDay(dateRange.from);
    const to = dateRange.to ? endOfDay(dateRange.to) : endOfDay(dateRange.from);
    
    return isWithinInterval(date, { start: from, end: to });
  } catch (error) {
    console.error('Error parsing date:', dateString, error);
    return false;
  }
};

/**
 * Filter array of items by date range
 */
export const filterByDateRange = <T extends Record<string, any>>(
  items: T[],
  dateRange: DateRange | undefined,
  dateField: keyof T = 'created_at'
): T[] => {
  if (!dateRange?.from) return items;
  
  return items.filter(item => {
    const dateValue = item[dateField];
    if (!dateValue) return false;
    
    // Handle both string dates and Date objects
    const dateString = typeof dateValue === 'string' ? dateValue : dateValue.toISOString();
    return isDateInRange(dateString, dateRange);
  });
};

/**
 * Filter transactions by date range
 */
export const filterTransactionsByDate = (
  transactions: any[],
  dateRange: DateRange | undefined
) => {
  return filterByDateRange(transactions, dateRange, 'date');
};

/**
 * Filter receipts by date range
 */
export const filterReceiptsByDate = (
  receipts: any[],
  dateRange: DateRange | undefined
) => {
  return filterByDateRange(receipts, dateRange, 'created_at');
};

/**
 * Filter expenses by date range
 */
export const filterExpensesByDate = (
  expenses: any[],
  dateRange: DateRange | undefined
) => {
  return filterByDateRange(expenses, dateRange, 'date');
};

/**
 * Filter inventory movements by date range
 */
export const filterInventoryByDate = (
  inventory: any[],
  dateRange: DateRange | undefined
) => {
  return filterByDateRange(inventory, dateRange, 'updated_at');
};

/**
 * Calculate totals for filtered data
 */
export const calculateFilteredTotals = (
  items: any[],
  dateRange: DateRange | undefined,
  amountField: string = 'amount'
) => {
  const filteredItems = filterByDateRange(items, dateRange);
  
  return {
    total: filteredItems.reduce((sum, item) => sum + (Number(item[amountField]) || 0), 0),
    count: filteredItems.length,
    items: filteredItems
  };
};

/**
 * Get date range summary text
 */
export const getDateRangeSummary = (dateRange: DateRange | undefined): string => {
  if (!dateRange?.from) return "All time";
  
  const from = dateRange.from;
  const to = dateRange.to || dateRange.from;
  
  if (from.getTime() === to.getTime()) {
    return from.toLocaleDateString();
  }
  
  return `${from.toLocaleDateString()} - ${to.toLocaleDateString()}`;
};

/**
 * Group data by month for charts
 */
export const groupByMonth = <T extends Record<string, any>>(
  items: T[],
  dateRange: DateRange | undefined,
  dateField: keyof T = 'created_at',
  valueField: keyof T = 'amount'
) => {
  const filteredItems = filterByDateRange(items, dateRange, dateField);
  
  const monthlyData = new Map<string, number>();
  
  filteredItems.forEach(item => {
    const dateValue = item[dateField];
    if (!dateValue) return;
    
    try {
      const date = typeof dateValue === 'string' ? parseISO(dateValue) : dateValue;
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      const value = Number(item[valueField]) || 0;
      
      monthlyData.set(monthKey, (monthlyData.get(monthKey) || 0) + value);
    } catch (error) {
      console.error('Error processing date for grouping:', dateValue, error);
    }
  });
  
  return Array.from(monthlyData.entries()).map(([month, value]) => ({
    month,
    value,
    label: new Date(month + '-01').toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
  })).sort((a, b) => a.month.localeCompare(b.month));
};

/**
 * Get period comparison data
 */
export const getPeriodComparison = <T extends Record<string, any>>(
  items: T[],
  currentRange: DateRange | undefined,
  previousRange: DateRange | undefined,
  valueField: keyof T = 'amount'
) => {
  const currentData = filterByDateRange(items, currentRange);
  const previousData = filterByDateRange(items, previousRange);
  
  const currentTotal = currentData.reduce((sum, item) => sum + (Number(item[valueField]) || 0), 0);
  const previousTotal = previousData.reduce((sum, item) => sum + (Number(item[valueField]) || 0), 0);
  
  const change = currentTotal - previousTotal;
  const percentageChange = previousTotal > 0 ? (change / previousTotal) * 100 : 0;
  
  return {
    current: {
      total: currentTotal,
      count: currentData.length,
      items: currentData
    },
    previous: {
      total: previousTotal,
      count: previousData.length,
      items: previousData
    },
    change: {
      absolute: change,
      percentage: percentageChange,
      isPositive: change >= 0
    }
  };
};
