
import { useState, useMemo } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, DollarSign, TrendingUp, Calendar, ShoppingCart } from "lucide-react";
import { DateFilter } from "@/components/DateFilter";
import { filterTransactionsByDate, getDateRangeSummary } from "@/utils/dateFilters";
import { useTransactions } from "@/hooks/useTransactions";
import { useProducts } from "@/hooks/useProducts";
import { useServices } from "@/hooks/useServices";
import { useContacts } from "@/hooks/useContacts";
import { AddSaleForm } from "@/components/forms/AddSaleForm";
import { useToast } from "@/components/ui/use-toast";
import { useCurrency } from "@/contexts/CurrencyContext";
import { formatCurrency } from "@/utils/currency";
import { useSubscriptionContext } from "@/components/SubscriptionProvider";
import { useReceipts } from "@/hooks/useReceipts";
import { convertTransactionToReceipt } from "@/utils/salesToReceipt";
import { DateRange } from "react-day-picker";

export default function Sales() {
  const { user } = useSubscriptionContext();
  const { transactions, loading: transactionsLoading, addTransaction } = useTransactions(user);
  const { products, loading: productsLoading, updateProduct } = useProducts(user);
  const { services, loading: servicesLoading, updateService } = useServices();
  const { contacts, loading: contactsLoading } = useContacts();
  const { addReceipt } = useReceipts();
  const { currency } = useCurrency();
  const [showAddForm, setShowAddForm] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const { toast } = useToast();

  const loading = transactionsLoading || productsLoading || servicesLoading || contactsLoading;

  const handleAddSale = async (saleData: any) => {
    try {
      const customerName = saleData.customerId
        ? contacts.find(c => c.id === saleData.customerId)?.full_name ||
          contacts.find(c => c.id === saleData.customerId)?.company || 'Unknown Customer'
        : 'Walk-in Customer';

      // Generate receipt using our receipt system
      const transactionReceiptData = {
        description: saleData.itemName || saleData.description || 'Manual Sale',
        amount: saleData.amount,
        customer: customerName,
        category: saleData.itemType || 'sale',
        notes: `Manual Sale - Payment: ${saleData.paymentMethod || 'cash'}`,
      };

      const receiptData = convertTransactionToReceipt(transactionReceiptData);

      // Create the receipt (this will automatically handle stock updates and revenue tracking)
      console.log('Creating receipt for manual sale:', receiptData);
      await addReceipt(receiptData);

      // Map the data to match the database schema for legacy transaction tracking
      const transactionData = {
        type: 'sale' as const,
        description: saleData.itemName || saleData.description || 'Sale',
        amount: saleData.amount,
        category: saleData.itemType || 'sale',
        customer: customerName,
        product: saleData.itemName,
        payment_method: saleData.paymentMethod || 'cash',
        date: new Date().toISOString().split('T')[0]
      };

      console.log('Adding sale transaction:', transactionData);

      // Add the transaction (for legacy tracking)
      await addTransaction(transactionData);

      // Note: Stock and service updates are now handled by the receipt system automatically
      // The following code is kept for backward compatibility but may be redundant

      // If it's a product sale, update the inventory (handled by receipt system now)
      if (saleData.itemType === 'product' && saleData.productId) {
        const product = products.find(p => p.id === saleData.productId);
        if (product) {
          const newStock = Math.max(0, (product.stock || 0) - (saleData.quantity || 1));
          await updateProduct(product.id, {
            stock: newStock
          });
        }
      }

      // If it's a service sale, update service sales tracking (handled by receipt system now)
      if (saleData.itemType === 'service' && saleData.serviceId) {
        const service = services.find(s => s.id === saleData.serviceId);
        if (service) {
          await updateService(service.id, {
            total_sales: (service.total_sales || 0) + 1,
            total_revenue: (service.total_revenue || 0) + saleData.amount
          });
        }
      }

      toast({
        title: 'Success',
        description: 'Sale added and receipt generated successfully',
      });
      setShowAddForm(false);
    } catch (error) {
      console.error('Error adding sale:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to add sale',
      });
    }
  };

  // Filter sales by date range using utility function
  const salesTransactions = transactions.filter(t => t.type === 'sale');
  const filteredSales = useMemo(() => {
    return filterTransactionsByDate(salesTransactions, dateRange);
  }, [salesTransactions, dateRange]);

  const totalRevenue = filteredSales.reduce((sum, sale) => sum + Number(sale.amount), 0);
  
  // Get unique customers
  const customers = Array.from(new Set(filteredSales.map(s => s.customer).filter(Boolean))).length;
  
  // This month revenue
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  const thisMonthRevenue = filteredSales
    .filter(sale => {
      const saleDate = new Date(sale.date);
      return saleDate.getMonth() === currentMonth && saleDate.getFullYear() === currentYear;
    })
    .reduce((sum, sale) => sum + Number(sale.amount), 0);
  
  const averageDailyRevenue = filteredSales.length > 0 ? totalRevenue / 30 : 0; // Simplified calculation

  // Transform data for AddSaleForm
  const transformedProducts = products.map(product => ({
    id: product.id,
    name: product.name,
    price: product.price,
    type: 'product' as const,
    stock_quantity: product.stock,
    description: product.category
  }));

  const transformedServices = services.map(service => ({
    id: service.id,
    name: service.name,
    price: service.price,
    type: 'service' as const,
    description: service.description
  }));

  const transformedContacts = contacts.map(contact => ({
    id: contact.id,
    name: contact.full_name,
    email: contact.email,
    phone: contact.phone
  }));

  if (loading) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Sales</h1>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Sales</h1>
            <p className="text-muted-foreground">
              Track and manage your sales transactions
              {dateRange && (
                <span className="ml-2 text-sm">
                  • Filtered by {getDateRangeSummary(dateRange)}
                </span>
              )}
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={() => setShowAddForm(true)}>
              <Plus className="mr-2 h-4 w-4" /> Add Sale
            </Button>
          </div>
        </div>

        {/* Date Filter */}
        <div className="flex items-center gap-4">
          <DateFilter
            dateRange={dateRange}
            onDateRangeChange={setDateRange}
            showQuickFilters={true}
            showMonthFilter={true}
          />
          {filteredSales.length !== salesTransactions.length && (
            <Badge variant="secondary">
              Showing {filteredSales.length} of {salesTransactions.length} sales
            </Badge>
          )}
        </div>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalRevenue, currency || 'KES')}</div>
            <p className="text-xs text-muted-foreground">
              {dateRange?.from || dateRange?.to ? 'Filtered period' : 'All time sales'}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customers</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{customers}</div>
            <p className="text-xs text-muted-foreground">Unique customers</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(thisMonthRevenue, currency || 'KES')}</div>
            <p className="text-xs text-muted-foreground">Current month</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Daily Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(averageDailyRevenue, currency || 'KES')}</div>
            <p className="text-xs text-muted-foreground">Average per day</p>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>
            Sales {dateRange?.from || dateRange?.to ? '(Filtered)' : ''}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredSales.length === 0 ? (
              <p className="text-center text-muted-foreground py-8">
                {dateRange?.from || dateRange?.to ? 'No sales found for selected period' : 'No sales recorded yet'}
              </p>
            ) : (
              filteredSales.slice(0, 10).map((sale) => (
                <div key={sale.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">{sale.description}</p>
                    <p className="text-sm text-muted-foreground capitalize">
                      {sale.customer}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-green-600">{formatCurrency(Number(sale.amount), currency || 'KES')}</p>
                    <p className="text-sm text-muted-foreground">{sale.date}</p>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Sale Form Modal */}
      {showAddForm && (
        <AddSaleForm 
          onClose={() => setShowAddForm(false)}
          onSubmit={handleAddSale}
          products={transformedProducts}
          services={transformedServices}
          contacts={transformedContacts}
        />
      )}
    </div>
  );
}
