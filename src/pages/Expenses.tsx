
import { useState, useMemo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, DollarSign, TrendingDown, Calendar, FileText } from "lucide-react";
import { DateFilter } from "@/components/DateFilter";
import { filterExpensesByDate, calculateFilteredTotals, getDateRangeSummary } from "@/utils/dateFilters";
import { DateRange } from "react-day-picker";
import { useTransactions } from "@/hooks/useTransactions";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useCurrency } from "@/contexts/CurrencyContext";
import { formatCurrency } from "@/utils/currency";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useSubscriptionContext } from "@/components/SubscriptionProvider";

const expenseFormSchema = z.object({
  description: z.string().min(1, "Description is required"),
  category: z.string().min(1, "Category is required"),
  amount: z.number().min(0.01, "Amount must be greater than 0"),
  date: z.string().min(1, "Date is required"),
});

type ExpenseFormValues = z.infer<typeof expenseFormSchema>;

export default function Expenses() {
  const { user } = useSubscriptionContext();
  const { transactions, loading, addTransaction } = useTransactions(user);
  const { currency } = useCurrency();
  const [showAddForm, setShowAddForm] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  const form = useForm<ExpenseFormValues>({
    resolver: zodResolver(expenseFormSchema),
    defaultValues: {
      description: "",
      category: "",
      amount: 0,
      date: new Date().toISOString().split('T')[0],
    },
  });

  const onSubmit = async (values: ExpenseFormValues) => {
    try {
      await addTransaction({
        description: values.description, // Ensure required field is present
        amount: values.amount,
        category: values.category,
        date: values.date,
        type: 'expense',
      });
      setShowAddForm(false);
      form.reset();
    } catch (error) {
      console.error('Error adding expense:', error);
    }
  };

  // Filter expenses by date range
  const allExpenses = transactions.filter(t => t.type === 'expense');
  const filteredExpenses = useMemo(() => {
    return filterExpensesByDate(allExpenses, dateRange);
  }, [allExpenses, dateRange]);

  // Calculate filtered metrics
  const { total: totalExpenses } = useMemo(() => {
    return calculateFilteredTotals(filteredExpenses, undefined, 'amount');
  }, [filteredExpenses]);

  // Get unique categories from filtered data
  const categories = Array.from(new Set(filteredExpenses.map(e => e.category).filter(Boolean))).length;

  // Calculate average daily spend based on filtered data
  const averageDailySpend = filteredExpenses.length > 0 ? totalExpenses / Math.max(filteredExpenses.length, 1) : 0;

  // Calculate this month's expenses
  const thisMonthExpenses = useMemo(() => {
    const now = new Date();
    const thisMonth = allExpenses.filter(expense => {
      const expenseDate = new Date(expense.date);
      return expenseDate.getMonth() === now.getMonth() && expenseDate.getFullYear() === now.getFullYear();
    });
    return thisMonth.reduce((sum, expense) => sum + (Number(expense.amount) || 0), 0);
  }, [allExpenses]);

  if (loading) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Expenses</h1>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Expenses</h1>
            <p className="text-muted-foreground">
              Track and manage your business expenses
              {dateRange && (
                <span className="ml-2 text-sm">
                  • Filtered by {getDateRangeSummary(dateRange)}
                </span>
              )}
            </p>
          </div>
          <div>
            <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add Expense
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Expense</DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter expense description" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="office-supplies">Office Supplies</SelectItem>
                            <SelectItem value="software">Software</SelectItem>
                            <SelectItem value="marketing">Marketing</SelectItem>
                            <SelectItem value="travel">Travel</SelectItem>
                            <SelectItem value="utilities">Utilities</SelectItem>
                            <SelectItem value="rent">Rent</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Amount</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            placeholder="0.00" 
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="submit" className="w-full">Add Expense</Button>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
          </div>
        </div>

        {/* Date Filter */}
        <div className="flex items-center gap-4">
          <DateFilter
            dateRange={dateRange}
            onDateRangeChange={setDateRange}
            showQuickFilters={true}
            showMonthFilter={true}
          />
          {filteredExpenses.length !== allExpenses.length && (
            <Badge variant="secondary">
              Showing {filteredExpenses.length} of {allExpenses.length} expenses
            </Badge>
          )}
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalExpenses, currency || 'KES')}</div>
            <p className="text-xs text-muted-foreground">All time expenses</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categories}</div>
            <p className="text-xs text-muted-foreground">Active categories</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${thisMonthExpenses.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">Current month</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Daily Spend</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${averageDailySpend.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">Average per day</p>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Recent Expenses</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredExpenses.length === 0 ? (
              <p className="text-center text-muted-foreground py-8">
                {allExpenses.length === 0 ? 'No expenses recorded yet' : 'No expenses found for selected date range'}
              </p>
            ) : (
              filteredExpenses.slice(0, 10).map((expense) => (
                <div key={expense.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">{expense.description}</p>
                    <p className="text-sm text-muted-foreground capitalize">
                      {expense.category?.replace('-', ' ')}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-red-600">{formatCurrency(Number(expense.amount), currency || 'KES')}</p>
                    <p className="text-sm text-muted-foreground">{expense.date}</p>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
