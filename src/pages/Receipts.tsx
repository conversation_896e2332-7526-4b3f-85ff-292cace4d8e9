import { useState, useMemo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, FileText, DollarSign, Clock, CheckCircle, Eye, Printer, Download, MoreVertical, Trash2, Edit, RefreshCw } from "lucide-react";
import { DateFilter } from "@/components/DateFilter";
import { filterReceiptsByDate, calculateFilteredTotals, getDateRangeSummary } from "@/utils/dateFilters";
import { DateRange } from "react-day-picker";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator, DropdownMenuSub, DropdownMenuSubContent, DropdownMenuSubTrigger } from "@/components/ui/dropdown-menu";
import { CreateReceiptForm } from "@/components/forms/CreateReceiptForm";
import { ReceiptViewer } from "@/components/ReceiptViewer";
import { useReceipts } from "@/hooks/useReceipts";
import { useCurrency } from "@/contexts/CurrencyContext";
import { formatCurrency } from "@/utils/currency";
import { format } from "date-fns";

export default function Receipts() {
  const { receipts, loading, addReceipt, updateReceiptStatus, deleteReceipt, refetch } = useReceipts();
  const { currency } = useCurrency();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedReceipt, setSelectedReceipt] = useState<any>(null);
  const [showReceiptViewer, setShowReceiptViewer] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  const handleCreateReceipt = async (newReceipt: any) => {
    try {
      console.log('Creating receipt from form:', newReceipt);
      const result = await addReceipt(newReceipt);
      console.log('Receipt created successfully:', result);
      setShowCreateForm(false);
      // Force refresh the receipts list
      await handleRefresh();
    } catch (error) {
      console.error('Error in handleCreateReceipt:', error);
      // Error handled by hook
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing receipts:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleViewReceipt = (receipt: any) => {
    setSelectedReceipt(receipt);
    setShowReceiptViewer(true);
  };

  const handleUpdateReceiptStatus = async (receiptId: string, newStatus: string, receiptItems?: any[]) => {
    try {
      await updateReceiptStatus(receiptId, newStatus, receiptItems);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleDeleteReceipt = async (receiptId: string) => {
    if (window.confirm('Are you sure you want to delete this receipt?')) {
      try {
        await deleteReceipt(receiptId);
      } catch (error) {
        // Error handled by hook
      }
    }
  };

  // Filter receipts by date range
  const filteredReceipts = useMemo(() => {
    return filterReceiptsByDate(receipts, dateRange);
  }, [receipts, dateRange]);

  // Calculate filtered totals
  const { total: totalAmount, count: totalCount } = useMemo(() => {
    return calculateFilteredTotals(filteredReceipts, undefined, 'amount');
  }, [filteredReceipts]);

  const paidReceipts = filteredReceipts.filter(rec => rec.status === "paid").length;
  const overdueReceipts = filteredReceipts.filter(rec => rec.status === "overdue").length;
  const draftReceipts = filteredReceipts.filter(rec => rec.status === "draft").length;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-4 space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
              Receipt Management
            </h1>
            <p className="text-muted-foreground mt-1">
              Create, manage, and track your receipts
              {dateRange && (
                <span className="ml-2 text-sm">
                  • Filtered by {getDateRangeSummary(dateRange)}
                </span>
              )}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={handleRefresh}
              variant="outline"
              size="lg"
              disabled={refreshing}
            >
              <RefreshCw className={`mr-2 h-5 w-5 ${refreshing ? 'animate-spin' : ''}`} />
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
            <Button
              onClick={() => setShowCreateForm(true)}
              size="lg"
              className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white shadow-lg"
            >
              <Plus className="mr-2 h-5 w-5" /> Create Receipt
            </Button>
          </div>
        </div>

        {/* Date Filter */}
        <div className="flex items-center gap-4">
          <DateFilter
            dateRange={dateRange}
            onDateRangeChange={setDateRange}
            showQuickFilters={true}
            showMonthFilter={true}
          />
          {filteredReceipts.length !== receipts.length && (
            <Badge variant="secondary">
              Showing {filteredReceipts.length} of {receipts.length} receipts
            </Badge>
          )}
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700">Total Receipts</CardTitle>
            <div className="p-2 bg-blue-200 rounded-lg">
              <FileText className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-700">{filteredReceipts.length}</div>
            <p className="text-xs text-blue-600 mt-1">
              {dateRange ? 'Filtered receipts' : 'All receipts'}
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700">Total Amount</CardTitle>
            <div className="p-2 bg-green-200 rounded-lg">
              <DollarSign className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-700">
              {formatCurrency(totalAmount, currency || 'KES')}
            </div>
            <p className="text-xs text-green-600 mt-1">All receipts</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-200 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-emerald-700">Paid</CardTitle>
            <div className="p-2 bg-emerald-200 rounded-lg">
              <CheckCircle className="h-4 w-4 text-emerald-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-emerald-700">{paidReceipts}</div>
            <p className="text-xs text-emerald-600 mt-1">Completed payments</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-br from-amber-50 to-amber-100 border-amber-200 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-amber-700">Overdue</CardTitle>
            <div className="p-2 bg-amber-200 rounded-lg">
              <Clock className="h-4 w-4 text-amber-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-amber-700">{overdueReceipts}</div>
            <p className="text-xs text-amber-600 mt-1">Need attention</p>
          </CardContent>
        </Card>
      </div>

      {/* Receipts List */}
      <Card className="shadow-lg border-0">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-green-600" />
            Receipts
            <Badge className="bg-green-100 text-green-700">
              {receipts.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredReceipts.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              {receipts.length === 0 ? (
                <>
                  <p className="text-muted-foreground text-lg">No receipts created yet</p>
                  <p className="text-sm text-muted-foreground">Create your first receipt to get started</p>
                </>
              ) : (
                <>
                  <p className="text-muted-foreground text-lg">No receipts found for selected date range</p>
                  <p className="text-sm text-muted-foreground">Try adjusting your date filter or create a new receipt</p>
                </>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredReceipts.map((receipt) => (
                <Card key={receipt.id} className="border-2 hover:border-green-300 transition-all duration-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-lg">#{receipt.receipt_number || receipt.invoice_number}</h3>
                          <Badge 
                            variant={
                              receipt.status === 'paid' ? 'default' : 
                              receipt.status === 'overdue' ? 'destructive' : 
                              'secondary'
                            }
                            className={
                              receipt.status === 'paid' ? 'bg-green-500 text-white' :
                              receipt.status === 'overdue' ? 'bg-red-500 text-white' :
                              'bg-yellow-500 text-white'
                            }
                          >
                            {receipt.status === 'paid' && <CheckCircle className="h-3 w-3 mr-1" />}
                            {receipt.status === 'overdue' && <Clock className="h-3 w-3 mr-1" />}
                            {receipt.status.charAt(0).toUpperCase() + receipt.status.slice(1)}
                          </Badge>
                        </div>
                        <p className="text-muted-foreground mb-1">Customer: {receipt.customer}</p>
                        <div className="flex gap-4 text-sm text-muted-foreground">
                          <span>Due: {format(new Date(receipt.due_date), 'MMM dd, yyyy')}</span>
                          <span>Created: {format(new Date(receipt.created_at), 'MMM dd, yyyy')}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="text-2xl font-bold text-green-600">
                            {formatCurrency(Number(receipt.amount), currency || 'KES')}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewReceipt(receipt)}
                            className="flex items-center gap-1"
                          >
                            <Eye className="h-4 w-4" />
                            View
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleViewReceipt(receipt)}>
                                <Eye className="mr-2 h-4 w-4" />
                                <span>View & Print</span>
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuSub>
                                <DropdownMenuSubTrigger>
                                  <Edit className="mr-2 h-4 w-4" />
                                  <span>Change Status</span>
                                </DropdownMenuSubTrigger>
                                <DropdownMenuSubContent>
                                  <DropdownMenuItem
                                    onClick={() => handleUpdateReceiptStatus(receipt.id, 'draft', receipt.items)}
                                    disabled={receipt.status === 'draft'}
                                  >
                                    Draft
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => handleUpdateReceiptStatus(receipt.id, 'pending', receipt.items)}
                                    disabled={receipt.status === 'pending'}
                                  >
                                    Pending
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => handleUpdateReceiptStatus(receipt.id, 'paid', receipt.items)}
                                    disabled={receipt.status === 'paid'}
                                    className="text-green-600"
                                  >
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Mark as Paid
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => handleUpdateReceiptStatus(receipt.id, 'overdue', receipt.items)}
                                    disabled={receipt.status === 'overdue'}
                                    className="text-red-600"
                                  >
                                    <Clock className="mr-2 h-4 w-4" />
                                    Mark as Overdue
                                  </DropdownMenuItem>
                                </DropdownMenuSubContent>
                              </DropdownMenuSub>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-red-600"
                                onClick={() => handleDeleteReceipt(receipt.id)}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                <span>Delete</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Receipt Form */}
      {showCreateForm && (
        <CreateReceiptForm
          onClose={() => setShowCreateForm(false)}
          onSubmit={handleCreateReceipt}
        />
      )}

      {/* Receipt Viewer */}
      {selectedReceipt && (
        <ReceiptViewer
          receipt={selectedReceipt}
          isOpen={showReceiptViewer}
          onClose={() => {
            setShowReceiptViewer(false);
            setSelectedReceipt(null);
          }}
        />
      )}
    </div>
  );
}
