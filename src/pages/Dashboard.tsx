
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { DollarSign, TrendingUp, TrendingDown, Users, ShoppingCart, Package, Plus } from "lucide-react";
import { useTransactions } from "@/hooks/useTransactions";
import { useProducts } from "@/hooks/useProducts";
import { useServices } from "@/hooks/useServices";
import { useContacts } from "@/hooks/useContacts";
import { useRevenue } from "@/hooks/useRevenue";
import { LineChart, Line, AreaChart, Area, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { useMemo, useState } from 'react';
import { Button } from "@/components/ui/button";
import { AddSaleForm } from "@/components/forms/AddSaleForm";
import { AddExpenseForm } from "@/components/forms/AddExpenseForm";
import { useToast } from "@/components/ui/use-toast";
import { useCurrency } from "@/contexts/CurrencyContext";
import { formatCurrency } from "@/utils/currency";
import { useSubscriptionContext } from "@/components/SubscriptionProvider";
import { useDataRefresh } from "@/contexts/DataRefreshContext";

export default function Dashboard() {
  const { user } = useSubscriptionContext();
  const { transactions, loading: transactionsLoading, addTransaction } = useTransactions(user);
  const { products, loading: productsLoading, updateProduct } = useProducts(user);
  const { services, loading: servicesLoading, updateService } = useServices();
  const { contacts, loading: contactsLoading } = useContacts();
  const { currency } = useCurrency();
  const { toast } = useToast();
  const { refreshAll } = useDataRefresh();
  const [showSaleForm, setShowSaleForm] = useState(false);
  const [showExpenseForm, setShowExpenseForm] = useState(false);

  const loading = transactionsLoading || productsLoading || servicesLoading || contactsLoading;

  const analytics = useMemo(() => {
    const sales = transactions.filter(t => t.type === 'sale');
    const expenses = transactions.filter(t => t.type === 'expense');

    const totalRevenue = sales.reduce((sum, sale) => sum + Number(sale.amount), 0);
    const totalExpenses = expenses.reduce((sum, expense) => sum + Number(expense.amount), 0);
    const netProfit = totalRevenue - totalExpenses;

    // Calculate monthly data for charts
    const monthlyData = transactions.reduce((acc, transaction) => {
      const month = new Date(transaction.date).toLocaleDateString('en-US', { month: 'short' });
      const existing = acc.find(item => item.month === month);

      if (existing) {
        if (transaction.type === 'sale') {
          existing.sales += Number(transaction.amount);
        } else {
          existing.expenses += Number(transaction.amount);
        }
      } else {
        acc.push({
          month,
          sales: transaction.type === 'sale' ? Number(transaction.amount) : 0,
          expenses: transaction.type === 'expense' ? Number(transaction.amount) : 0,
        });
      }
      return acc;
    }, [] as Array<{ month: string; sales: number; expenses: number }>);

    // Expense breakdown
    const expenseBreakdown = expenses.reduce((acc, expense) => {
      const category = expense.category || 'Other';
      acc[category] = (acc[category] || 0) + Number(expense.amount);
      return acc;
    }, {} as Record<string, number>);

    const expenseChartData = Object.entries(expenseBreakdown).map(([name, value]) => ({
      name,
      value,
    }));

    return {
      totalRevenue,
      totalExpenses,
      netProfit,
      totalSales: sales.length,
      inventoryValue: products.reduce((sum, product) => sum + (Number(product.price) * product.stock), 0),
      activeCustomers: contacts.filter(c => c.status === 'active' || c.status === 'customer').length,
      monthlyData,
      expenseChartData,
    };
  }, [transactions, products, contacts]);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  const handleAddSale = async (saleData: any) => {
    try {
      // Map the data to match the database schema
      const transactionData = {
        type: 'sale' as const,
        description: saleData.itemName || saleData.description || 'Sale',
        amount: saleData.amount,
        category: saleData.itemType || 'sale',
        customer: saleData.customerId ? contacts.find(c => c.id === saleData.customerId)?.full_name || 'Unknown Customer' : 'Walk-in Customer',
        product: saleData.itemName,
        payment_method: saleData.paymentMethod || 'cash',
        date: new Date().toISOString().split('T')[0]
      };

      console.log('Adding sale transaction:', transactionData);
      
      // Add the transaction
      await addTransaction(transactionData);

      // If it's a product sale, update the inventory
      if (saleData.itemType === 'product' && saleData.productId) {
        const product = products.find(p => p.id === saleData.productId);
        if (product) {
          const newStock = Math.max(0, (product.stock || 0) - (saleData.quantity || 1));
          await updateProduct(product.id, {
            stock: newStock
          });
        }
      }

      // If it's a service sale, update service sales tracking
      if (saleData.itemType === 'service' && saleData.serviceId) {
        const service = services.find(s => s.id === saleData.serviceId);
        if (service) {
          await updateService(service.id, {
            total_sales: (service.total_sales || 0) + 1,
            total_revenue: (service.total_revenue || 0) + saleData.amount
          });
        }
      }

      toast({
        title: 'Success',
        description: 'Sale added successfully',
      });

      // Trigger data refresh across all pages
      refreshAll();

      setShowSaleForm(false);
    } catch (error) {
      console.error('Error adding sale:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to add sale',
      });
    }
  };

  const handleAddExpense = async (expenseData: any) => {
    try {
      // Map the data to match the database schema
      const transactionData = {
        type: 'expense' as const,
        description: expenseData.description,
        amount: expenseData.amount,
        category: expenseData.category,
        date: expenseData.date,
        payment_method: expenseData.paymentMethod || 'cash'
      };

      console.log('Adding expense transaction:', transactionData);
      
      await addTransaction(transactionData);
      
      toast({
        title: 'Success',
        description: 'Expense added successfully',
      });

      // Trigger data refresh across all pages
      refreshAll();

      setShowExpenseForm(false);
    } catch (error) {
      console.error('Error adding expense:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to add expense',
      });
    }
  };

  // Transform data for AddSaleForm
  const transformedProducts = products.map(product => ({
    id: product.id,
    name: product.name,
    price: product.price,
    type: 'product' as const,
    stock_quantity: product.stock,
    description: product.category
  }));

  const transformedServices = services.map(service => ({
    id: service.id,
    name: service.name,
    price: service.price,
    type: 'service' as const,
    description: service.description
  }));

  const transformedContacts = contacts.map(contact => ({
    id: contact.id,
    name: contact.full_name,
    email: contact.email,
    phone: contact.phone
  }));

  if (loading) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Dashboard</h1>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 px-4 sm:px-0">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">Overview of your business performance</p>
          </div>
          <div className="flex gap-2 w-full sm:w-auto">
          <Button 
            onClick={() => setShowSaleForm(true)}
            className="flex-1 sm:flex-none gap-2"
          >
            <Plus className="h-4 w-4" /> Add Sale
          </Button>
          <Button 
            variant="outline" 
            onClick={() => setShowExpenseForm(true)}
            className="flex-1 sm:flex-none gap-2"
          >
            <Plus className="h-4 w-4" /> Add Expense
          </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 px-4 sm:px-0">
        <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="p-4 pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-blue-500" />
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="text-xl font-bold">{formatCurrency(analytics.totalRevenue, currency || 'KES')}</div>
            <p className="text-xs text-muted-foreground mt-1">From {analytics.totalSales} sales</p>
          </CardContent>
        </Card>

        <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="p-4 pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-500" />
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="text-xl font-bold">{formatCurrency(analytics.totalExpenses, currency || 'KES')}</div>
            <p className="text-xs text-muted-foreground mt-1">This period</p>
          </CardContent>
        </Card>

        <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="p-4 pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
              <TrendingUp className={`h-4 w-4 ${analytics.netProfit >= 0 ? 'text-green-500' : 'text-red-500'}`} />
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className={`text-xl font-bold ${analytics.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatCurrency(Math.abs(analytics.netProfit), currency || 'KES')}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {analytics.netProfit >= 0 ? 'Profit' : 'Loss'} this period
            </p>
          </CardContent>
        </Card>
        
        <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="p-4 pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">Active Customers</CardTitle>
              <Users className="h-4 w-4 text-purple-500" />
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="text-xl font-bold">{analytics.activeCustomers}</div>
            <p className="text-xs text-muted-foreground mt-1">Active users</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-4 grid-cols-1 lg:grid-cols-2 px-4 sm:px-0">
        <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="p-4 pb-2">
            <CardTitle className="text-base font-semibold">Sales vs Expenses</CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="h-[280px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={analytics.monthlyData} margin={{ top: 10, right: 10, left: -20, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
                  <XAxis 
                    dataKey="month" 
                    tick={{ fontSize: 12 }}
                    axisLine={false}
                    tickLine={false}
                  />
                  <YAxis
                    tick={{ fontSize: 12 }}
                    axisLine={false}
                    tickLine={false}
                    tickFormatter={(value) => formatCurrency(value, currency || 'KES')}
                  />
                  <Tooltip
                    formatter={(value: number) => [formatCurrency(value, currency || 'KES'), '']}
                    contentStyle={{
                      borderRadius: '8px',
                      border: '1px solid #e5e7eb',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                      fontSize: '12px',
                    }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="sales" 
                    stroke="#10b981" 
                    strokeWidth={2} 
                    dot={{ r: 3 }}
                    activeDot={{ r: 5, stroke: '#10b981', strokeWidth: 2 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="expenses" 
                    stroke="#ef4444" 
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    activeDot={{ r: 5, stroke: '#ef4444', strokeWidth: 2 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div className="flex items-center justify-center gap-4 mt-2">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                <span className="text-xs text-gray-600">Sales</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                <span className="text-xs text-gray-600">Expenses</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="p-4 pb-2">
            <CardTitle className="text-base font-semibold">Expense Breakdown</CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="h-[280px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={analytics.expenseChartData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={80}
                    paddingAngle={2}
                    dataKey="value"
                    label={({ name, percent }) => `${(percent * 100).toFixed(0)}%`}
                    labelLine={false}
                  >
                    {analytics.expenseChartData.map((entry, index) => (
                      <Cell 
                        key={`cell-${index}`} 
                        fill={COLORS[index % COLORS.length]}
                        stroke="#fff"
                        strokeWidth={1}
                      />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value: number) => [formatCurrency(value, currency || 'KES'), 'Amount']}
                    contentStyle={{
                      borderRadius: '8px',
                      border: '1px solid #e5e7eb',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                      fontSize: '12px',
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-2 gap-2 mt-2">
              {analytics.expenseChartData.map((entry, index) => (
                <div key={`legend-${index}`} className="flex items-center">
                  <div 
                    className="w-3 h-3 rounded-full mr-2" 
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  ></div>
                  <span className="text-xs text-gray-600 truncate">{entry.name}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Transactions */}
      <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-shadow mx-4 sm:mx-0">
        <CardHeader className="p-4 pb-2">
          <CardTitle className="text-base font-semibold">Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {transactions.length === 0 ? (
            <div className="text-center py-8 px-4">
              <Package className="mx-auto h-8 w-8 text-gray-400 mb-2" />
              <p className="text-sm text-gray-500">No transactions yet</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {transactions.slice(0, 5).map((transaction) => (
                <div 
                  key={transaction.id} 
                  className="group p-4 hover:bg-gray-50 transition-colors duration-150"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <div className={`mt-0.5 flex h-8 w-8 items-center justify-center rounded-full ${transaction.type === 'sale' ? 'bg-green-100' : 'bg-red-100'}`}>
                        {transaction.type === 'sale' ? (
                          <ShoppingCart className="h-4 w-4 text-green-600" />
                        ) : (
                          <Package className="h-4 w-4 text-red-600" />
                        )}
                      </div>
                      <div className="min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {transaction.description}
                        </p>
                        <div className="flex flex-wrap items-center gap-x-2 gap-y-0.5 mt-1">
                          <span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-700">
                            {transaction.type}
                          </span>
                          {transaction.category && (
                            <span className="text-xs text-gray-500">
                              • {transaction.category}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right ml-2">
                      <p className={`text-sm font-medium ${transaction.type === 'sale' ? 'text-green-600' : 'text-red-600'}`}>
                        {transaction.type === 'sale' ? '+' : '-'}{formatCurrency(Number(transaction.amount), currency || 'KES')}
                      </p>
                      <p className="text-xs text-gray-500 mt-0.5">
                        {new Date(transaction.date).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                        })}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
              {transactions.length > 5 && (
                <div className="p-4 text-center">
                  <button className="text-sm font-medium text-blue-600 hover:text-blue-700">
                    View all transactions
                  </button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sale Form Modal */}
      {showSaleForm && (
        <AddSaleForm 
          onClose={() => setShowSaleForm(false)}
          onSubmit={handleAddSale}
          products={transformedProducts}
          services={transformedServices}
          contacts={transformedContacts}
        />
      )}

      {/* Expense Form Modal */}
      {showExpenseForm && (
        <AddExpenseForm 
          onClose={() => setShowExpenseForm(false)}
          onSubmit={handleAddExpense}
        />
      )}
    </div>
  );
}
