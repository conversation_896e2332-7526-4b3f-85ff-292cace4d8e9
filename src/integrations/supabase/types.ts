export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      companies: {
        Row: {
          created_at: string | null
          employees: string | null
          id: string
          industry: string | null
          location: string | null
          name: string
          revenue: string | null
          status: Database["public"]["Enums"]["company_status"]
          updated_at: string | null
          user_id: string | null
          website: string | null
        }
        Insert: {
          created_at?: string | null
          employees?: string | null
          id?: string
          industry?: string | null
          location?: string | null
          name: string
          revenue?: string | null
          status?: Database["public"]["Enums"]["company_status"]
          updated_at?: string | null
          user_id?: string | null
          website?: string | null
        }
        Update: {
          created_at?: string | null
          employees?: string | null
          id?: string
          industry?: string | null
          location?: string | null
          name?: string
          revenue?: string | null
          status?: Database["public"]["Enums"]["company_status"]
          updated_at?: string | null
          user_id?: string | null
          website?: string | null
        }
        Relationships: []
      }
      contacts: {
        Row: {
          company: string | null
          created_at: string | null
          email: string | null
          full_name: string
          id: string
          notes: string | null
          phone: string | null
          status: Database["public"]["Enums"]["contact_status"]
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          company?: string | null
          created_at?: string | null
          email?: string | null
          full_name: string
          id?: string
          notes?: string | null
          phone?: string | null
          status?: Database["public"]["Enums"]["contact_status"]
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          company?: string | null
          created_at?: string | null
          email?: string | null
          full_name?: string
          id?: string
          notes?: string | null
          phone?: string | null
          status?: Database["public"]["Enums"]["contact_status"]
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      deals: {
        Row: {
          amount: number
          close_date: string | null
          company_id: string | null
          created_at: string | null
          id: string
          name: string
          owner: string | null
          probability: number | null
          stage: Database["public"]["Enums"]["deal_stage"]
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          amount?: number
          close_date?: string | null
          company_id?: string | null
          created_at?: string | null
          id?: string
          name: string
          owner?: string | null
          probability?: number | null
          stage?: Database["public"]["Enums"]["deal_stage"]
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          amount?: number
          close_date?: string | null
          company_id?: string | null
          created_at?: string | null
          id?: string
          name?: string
          owner?: string | null
          probability?: number | null
          stage?: Database["public"]["Enums"]["deal_stage"]
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "deals_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          amount: number
          created_at: string | null
          customer: string
          due_date: string
          id: string
          invoice_number: string
          items: Json | null
          notes: string | null
          status: Database["public"]["Enums"]["invoice_status"]
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          amount?: number
          created_at?: string | null
          customer: string
          due_date: string
          id?: string
          invoice_number: string
          items?: Json | null
          notes?: string | null
          status?: Database["public"]["Enums"]["invoice_status"]
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          amount?: number
          created_at?: string | null
          customer?: string
          due_date?: string
          id?: string
          invoice_number?: string
          items?: Json | null
          notes?: string | null
          status?: Database["public"]["Enums"]["invoice_status"]
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      notifications: {
        Row: {
          created_at: string
          id: string
          message: string
          read: boolean
          title: string
          type: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          message: string
          read?: boolean
          title: string
          type?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          message?: string
          read?: boolean
          title?: string
          type?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      products: {
        Row: {
          category: string
          created_at: string | null
          id: string
          name: string
          price: number
          sku: string
          status: Database["public"]["Enums"]["product_status"]
          stock: number
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          category: string
          created_at?: string | null
          id?: string
          name: string
          price?: number
          sku: string
          status?: Database["public"]["Enums"]["product_status"]
          stock?: number
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          id?: string
          name?: string
          price?: number
          sku?: string
          status?: Database["public"]["Enums"]["product_status"]
          stock?: number
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      quotes: {
        Row: {
          amount: number
          created_at: string | null
          customer: string
          date: string
          expiry_date: string | null
          id: string
          items: Json | null
          notes: string | null
          quote_number: string
          status: Database["public"]["Enums"]["quote_status"]
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          amount?: number
          created_at?: string | null
          customer: string
          date?: string
          expiry_date?: string | null
          id?: string
          items?: Json | null
          notes?: string | null
          quote_number: string
          status?: Database["public"]["Enums"]["quote_status"]
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          amount?: number
          created_at?: string | null
          customer?: string
          date?: string
          expiry_date?: string | null
          id?: string
          items?: Json | null
          notes?: string | null
          quote_number?: string
          status?: Database["public"]["Enums"]["quote_status"]
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      services: {
        Row: {
          category: string
          created_at: string | null
          description: string | null
          id: string
          name: string
          price: number
          status: Database["public"]["Enums"]["service_status"]
          total_revenue: number | null
          total_sales: number | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          category: string
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          price?: number
          status?: Database["public"]["Enums"]["service_status"]
          total_revenue?: number | null
          total_sales?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          price?: number
          status?: Database["public"]["Enums"]["service_status"]
          total_revenue?: number | null
          total_sales?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      tasks: {
        Row: {
          created_at: string | null
          description: string | null
          due_date: string | null
          id: string
          labels: string[] | null
          priority: Database["public"]["Enums"]["task_priority"]
          status: Database["public"]["Enums"]["task_status"]
          title: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          due_date?: string | null
          id?: string
          labels?: string[] | null
          priority?: Database["public"]["Enums"]["task_priority"]
          status?: Database["public"]["Enums"]["task_status"]
          title: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          due_date?: string | null
          id?: string
          labels?: string[] | null
          priority?: Database["public"]["Enums"]["task_priority"]
          status?: Database["public"]["Enums"]["task_status"]
          title?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      transactions: {
        Row: {
          amount: number
          category: string | null
          created_at: string | null
          customer: string | null
          date: string
          description: string
          id: string
          payment_method: string | null
          product: string | null
          type: Database["public"]["Enums"]["transaction_type"]
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          amount: number
          category?: string | null
          created_at?: string | null
          customer?: string | null
          date?: string
          description: string
          id?: string
          payment_method?: string | null
          product?: string | null
          type: Database["public"]["Enums"]["transaction_type"]
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          amount?: number
          category?: string | null
          created_at?: string | null
          customer?: string | null
          date?: string
          description?: string
          id?: string
          payment_method?: string | null
          product?: string | null
          type?: Database["public"]["Enums"]["transaction_type"]
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      user_settings: {
        Row: {
          avatar_url: string | null
          company_name: string | null
          country: string | null
          created_at: string | null
          currency: string | null
          display_name: string | null
          email: string | null
          id: string
          language: string | null
          timezone: string | null
          updated_at: string | null
          user_currency: string | null
          user_id: string | null
        }
        Insert: {
          avatar_url?: string | null
          company_name?: string | null
          country?: string | null
          created_at?: string | null
          currency?: string | null
          display_name?: string | null
          email?: string | null
          id?: string
          language?: string | null
          timezone?: string | null
          updated_at?: string | null
          user_currency?: string | null
          user_id?: string | null
        }
        Update: {
          avatar_url?: string | null
          company_name?: string | null
          country?: string | null
          created_at?: string | null
          currency?: string | null
          display_name?: string | null
          email?: string | null
          id?: string
          language?: string | null
          timezone?: string | null
          updated_at?: string | null
          user_currency?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      has_active_subscription: {
        Args: { user_uuid: string }
        Returns: boolean
      }
      is_trial_active: {
        Args: { user_uuid: string }
        Returns: boolean
      }
    }
    Enums: {
      company_status: "customer" | "lead" | "prospect" | "partner"
      contact_status: "active" | "inactive" | "lead" | "customer"
      deal_stage:
        | "new"
        | "qualified"
        | "proposal"
        | "negotiation"
        | "closed_won"
        | "closed_lost"
      invoice_status: "draft" | "sent" | "paid" | "overdue" | "cancelled"
      product_status: "in_stock" | "low_stock" | "out_of_stock"
      quote_status: "draft" | "sent" | "accepted" | "rejected" | "expired"
      service_status: "active" | "inactive"
      task_priority: "low" | "medium" | "high"
      task_status: "todo" | "in_progress" | "done"
      transaction_type: "sale" | "expense"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      company_status: ["customer", "lead", "prospect", "partner"],
      contact_status: ["active", "inactive", "lead", "customer"],
      deal_stage: [
        "new",
        "qualified",
        "proposal",
        "negotiation",
        "closed_won",
        "closed_lost",
      ],
      invoice_status: ["draft", "sent", "paid", "overdue", "cancelled"],
      product_status: ["in_stock", "low_stock", "out_of_stock"],
      quote_status: ["draft", "sent", "accepted", "rejected", "expired"],
      service_status: ["active", "inactive"],
      task_priority: ["low", "medium", "high"],
      task_status: ["todo", "in_progress", "done"],
      transaction_type: ["sale", "expense"],
    },
  },
} as const
