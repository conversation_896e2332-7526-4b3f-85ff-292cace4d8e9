import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { CalendarIcon, X, Filter } from "lucide-react";
import { format, startOfMonth, endOfMonth, startOfYear, endOfYear, subDays, subMonths, subYears } from "date-fns";
import { DateRange } from "react-day-picker";
import { cn } from "@/lib/utils";

interface DateFilterProps {
  dateRange: DateRange | undefined;
  onDateRangeChange: (range: DateRange | undefined) => void;
  className?: string;
  showQuickFilters?: boolean;
  showMonthFilter?: boolean;
}

const quickFilters = [
  { label: "Today", value: "today" },
  { label: "Yesterday", value: "yesterday" },
  { label: "Last 7 days", value: "last7days" },
  { label: "Last 30 days", value: "last30days" },
  { label: "This Month", value: "thisMonth" },
  { label: "Last Month", value: "lastMonth" },
  { label: "This Year", value: "thisYear" },
  { label: "Last Year", value: "lastYear" },
];

const monthOptions = [
  { label: "January", value: "0" },
  { label: "February", value: "1" },
  { label: "March", value: "2" },
  { label: "April", value: "3" },
  { label: "May", value: "4" },
  { label: "June", value: "5" },
  { label: "July", value: "6" },
  { label: "August", value: "7" },
  { label: "September", value: "8" },
  { label: "October", value: "9" },
  { label: "November", value: "10" },
  { label: "December", value: "11" },
];

export function DateFilter({ 
  dateRange, 
  onDateRangeChange, 
  className,
  showQuickFilters = true,
  showMonthFilter = true 
}: DateFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<string>("");
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString());

  const handleQuickFilter = (filterValue: string) => {
    const today = new Date();
    let from: Date;
    let to: Date;

    switch (filterValue) {
      case "today":
        from = to = today;
        break;
      case "yesterday":
        from = to = subDays(today, 1);
        break;
      case "last7days":
        from = subDays(today, 6);
        to = today;
        break;
      case "last30days":
        from = subDays(today, 29);
        to = today;
        break;
      case "thisMonth":
        from = startOfMonth(today);
        to = endOfMonth(today);
        break;
      case "lastMonth":
        const lastMonth = subMonths(today, 1);
        from = startOfMonth(lastMonth);
        to = endOfMonth(lastMonth);
        break;
      case "thisYear":
        from = startOfYear(today);
        to = endOfYear(today);
        break;
      case "lastYear":
        const lastYear = subYears(today, 1);
        from = startOfYear(lastYear);
        to = endOfYear(lastYear);
        break;
      default:
        return;
    }

    onDateRangeChange({ from, to });
    setIsOpen(false);
  };

  const handleMonthFilter = () => {
    if (!selectedMonth || !selectedYear) return;

    const year = parseInt(selectedYear);
    const month = parseInt(selectedMonth);
    const from = new Date(year, month, 1);
    const to = endOfMonth(from);

    onDateRangeChange({ from, to });
    setIsOpen(false);
  };

  const clearFilter = () => {
    onDateRangeChange(undefined);
    setSelectedMonth("");
    setSelectedYear(new Date().getFullYear().toString());
  };

  const formatDateRange = (range: DateRange | undefined) => {
    if (!range?.from) return "Select date range";
    if (!range.to) return format(range.from, "MMM dd, yyyy");
    if (range.from.getTime() === range.to.getTime()) {
      return format(range.from, "MMM dd, yyyy");
    }
    return `${format(range.from, "MMM dd")} - ${format(range.to, "MMM dd, yyyy")}`;
  };

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 10 }, (_, i) => currentYear - i);

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "justify-start text-left font-normal",
              !dateRange && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {formatDateRange(dateRange)}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-4 space-y-4">
            {/* Quick Filters */}
            {showQuickFilters && (
              <div>
                <h4 className="font-medium text-sm mb-2">Quick Filters</h4>
                <div className="grid grid-cols-2 gap-2">
                  {quickFilters.map((filter) => (
                    <Button
                      key={filter.value}
                      variant="ghost"
                      size="sm"
                      onClick={() => handleQuickFilter(filter.value)}
                      className="justify-start"
                    >
                      {filter.label}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Month/Year Filter */}
            {showMonthFilter && (
              <div>
                <h4 className="font-medium text-sm mb-2">Filter by Month</h4>
                <div className="flex gap-2">
                  <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Month" />
                    </SelectTrigger>
                    <SelectContent>
                      {monthOptions.map((month) => (
                        <SelectItem key={month.value} value={month.value}>
                          {month.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={selectedYear} onValueChange={setSelectedYear}>
                    <SelectTrigger className="w-20">
                      <SelectValue placeholder="Year" />
                    </SelectTrigger>
                    <SelectContent>
                      {years.map((year) => (
                        <SelectItem key={year} value={year.toString()}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    size="sm"
                    onClick={handleMonthFilter}
                    disabled={!selectedMonth || !selectedYear}
                  >
                    Apply
                  </Button>
                </div>
              </div>
            )}

            {/* Custom Date Range */}
            <div>
              <h4 className="font-medium text-sm mb-2">Custom Range</h4>
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={onDateRangeChange}
                numberOfMonths={2}
              />
            </div>

            {/* Clear Filter */}
            {dateRange && (
              <div className="pt-2 border-t">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilter}
                  className="w-full"
                >
                  <X className="mr-2 h-4 w-4" />
                  Clear Filter
                </Button>
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>

      {/* Active Filter Badge */}
      {dateRange && (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Filter className="h-3 w-3" />
          {formatDateRange(dateRange)}
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0 hover:bg-transparent"
            onClick={clearFilter}
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      )}
    </div>
  );
}
